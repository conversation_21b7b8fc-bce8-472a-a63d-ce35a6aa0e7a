from utils import AES_Encrypt, enc, generate_captcha_key
import json
import requests
import re
import time
import logging
import datetime
import random
from urllib3.exceptions import InsecureRequestWarning
def get_date(day_offset: int=0):
    today = datetime.datetime.now().date()
    offset_day = today + datetime.timedelta(days=day_offset)
    tomorrow = offset_day.strftime("%Y-%m-%d")
    return tomorrow

class reserve:
    def __init__(self, sleep_time=0.2, max_attempt=50, enable_slider=False, reserve_next_day=False, debug=False):
        self.login_page = "https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid="
        self.url = "https://office.chaoxing.com/front/third/apps/seat/code?id={}&seatNum={}"
        # 新的选择座位页面URL，用于获取token
        self.select_url = "https://office.chaoxing.com/front/third/apps/seat/select?deptIdEnc={}&id={}&day={}&backLevel=2&fidEnc={}"
        self.submit_url = "https://office.chaoxing.com/data/apps/seat/submit"
        self.seat_url = "https://office.chaoxing.com/data/apps/seat/getusedtimes"
        self.login_url = "https://passport2.chaoxing.com/fanyalogin"

        # 新增的验证API端点
        self.entrance_config_url = "https://office.chaoxing.com/data/apps/seat/entrance/config"
        self.seat_index_url = "https://office.chaoxing.com/data/apps/seat/index"
        self.identity_verify_url = "https://office.chaoxing.com/data/apps/seat/identity/verify"

        self.token = ""
        self.success_times = 0
        self.fail_dict = []
        self.submit_msg = []
        self.requests = requests.session()
        self.token_pattern = re.compile("token = '(.*?)'")

        # 这些参数需要动态获取
        self.fidEnc = None  # 需要动态获取
        self.deptIdEnc = None  # 需要动态获取
        self.deptId = None  # 部门ID

        self.headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha.chaoxing.com",
            "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Windows"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        # 基础API请求头模板（referer会动态设置）
        self.api_headers_template = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "X-Requested-With": "XMLHttpRequest",
            "Sec-Ch-Ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Ch-Ua-Platform-Version": '"15.0.0"',
            "Sec-Ch-Ua-Arch": '"x86"',
            "Sec-Ch-Ua-Bitness": '"64"',
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Priority": "u=1, i",
            "Host": "office.chaoxing.com",
            "Connection": "keep-alive",
            "DNT": "1"
        }

        # 用于提交预约的请求头（移动端模拟，与成功请求保持一致）
        self.submit_headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "X-Requested-With": "XMLHttpRequest",
            "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "Sec-Ch-Ua-Mobile": "?1",
            "Sec-Ch-Ua-Platform": '"Android"',
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Priority": "u=1, i"
        }

        self.login_headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Sec-Ch-Ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Ch-Ua-Platform-Version": '"15.0.0"',
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Upgrade-Insecure-Requests": "1",
            "DNT": "1",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Host": "passport2.chaoxing.com"
        }

        self.sleep_time = sleep_time
        self.max_attempt = max_attempt
        self.enable_slider = enable_slider
        self.reserve_next_day = reserve_next_day
        self.debug = debug
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

    def get_api_headers(self, referer=None):
        """动态生成API请求头"""
        headers = self.api_headers_template.copy()
        if referer:
            headers["Referer"] = referer
        elif self.fidEnc:
            headers["Referer"] = f"https://office.chaoxing.com/front/apps/seat/index?fidEnc={self.fidEnc}"
        return headers

    def human_like_delay(self, min_delay=0.5, max_delay=2.0):
        """模拟人类行为的随机延迟"""
        delay = random.uniform(min_delay, max_delay)
        if self.debug:
            logging.info(f"人类行为延迟: {delay:.2f}秒")
        time.sleep(delay)

    def add_browser_fingerprint_headers(self, headers):
        """添加浏览器指纹相关的请求头"""
        # 添加一些随机的浏览器指纹信息
        headers["Sec-Ch-Viewport-Width"] = str(random.randint(1200, 1920))
        headers["Sec-Ch-Viewport-Height"] = str(random.randint(800, 1080))
        headers["Sec-Ch-Dpr"] = "1"
        headers["Sec-Ch-Device-Memory"] = str(random.choice([4, 8, 16]))
        headers["Sec-Ch-Prefers-Color-Scheme"] = "light"
        return headers

    
    # login and page token
    def _get_page_token(self, url):
        # 使用适当的请求头来获取页面token
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Sec-Ch-Ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Ch-Ua-Platform-Version": '"15.0.0"',
            "Sec-Ch-Ua-Arch": '"x86"',
            "Sec-Ch-Ua-Bitness": '"64"',
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Upgrade-Insecure-Requests": "1",
            "Priority": "u=0, i",
            "Host": "office.chaoxing.com",
            "Connection": "keep-alive",
            "DNT": "1"
        }

        # 添加浏览器指纹信息
        headers = self.add_browser_fingerprint_headers(headers)

        # 添加人类行为延迟
        self.human_like_delay(0.2, 0.8)

        response = self.requests.get(url=url, headers=headers, verify=False)
        if self.debug:
            logging.info(f"获取token页面状态码: {response.status_code}")

        html = response.content.decode('utf-8')

        # 尝试多种token提取模式
        token_patterns = [
            r'token: ["\']([^"\']+)["\']',
            r'token:["\']([^"\']+)["\']',
            r'"token":\s*["\']([^"\']+)["\']',
            r'var token = ["\']([^"\']+)["\']',
            r'token\s*=\s*["\']([^"\']+)["\']'
        ]

        token = ""
        for pattern in token_patterns:
            matches = re.findall(pattern, html)
            if matches:
                token = matches[0]
                break

        if self.debug:
            logging.info(f"从页面提取到token: {token}")
            if not token:
                # 如果没有找到token，输出页面内容的一部分用于调试
                logging.info(f"页面内容片段: {html[:500]}...")

        return token

    def get_login_status(self):
        self.requests.headers = self.login_headers
        self.requests.get(url=self.login_page, verify=False)

    def login(self, username, password):
        username = AES_Encrypt(username)
        password = AES_Encrypt(password)
        parm = {
            "fid": -1,
            "uname": username,
            "password": password,
            "refer": "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode%3Fid%3D4219%26seatNum%3D380",
            "t": True
        }
        jsons = self.requests.post(
            url=self.login_url, params=parm, verify=False)
        obj = jsons.json()
        if obj['status']:
            logging.info(f"User {username} login successfully")
            # 登录成功后获取必要的参数
            self.extract_required_params()
            # 执行验证步骤
            self.perform_verification_steps()
            return (True, '')
        else:
            logging.info(f"User {username} login failed. Please check you password and username! ")
            return (False, obj['msg2'])

    def extract_required_params(self):
        """从登录后的页面或API中提取必要的参数"""
        try:
            # 尝试访问座位系统主页来获取参数
            office_main_url = "https://office.chaoxing.com/"
            response = self.requests.get(office_main_url, verify=False)
            html = response.text

            # 尝试从HTML中提取fidEnc参数
            fidEnc_pattern = r'fidEnc["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            fidEnc_match = re.search(fidEnc_pattern, html)
            if fidEnc_match:
                self.fidEnc = fidEnc_match.group(1)
                if self.debug:
                    logging.info(f"从页面提取到fidEnc: {self.fidEnc}")

            # 尝试从HTML中提取deptId相关参数
            deptId_pattern = r'deptId["\']?\s*[:=]\s*["\']?(\d+)["\']?'
            deptId_match = re.search(deptId_pattern, html)
            if deptId_match:
                self.deptId = deptId_match.group(1)
                if self.debug:
                    logging.info(f"从页面提取到deptId: {self.deptId}")

            # 如果没有从HTML中找到，尝试通过其他方式获取
            if not self.fidEnc or not self.deptId:
                self.try_alternative_param_extraction()

        except Exception as e:
            if self.debug:
                logging.error(f"提取参数失败: {e}")
            # 使用默认值作为后备
            self.use_fallback_params()

    def try_alternative_param_extraction(self):
        """尝试通过其他方式获取参数"""
        try:
            # 尝试访问座位系统的其他页面
            urls_to_try = [
                "https://office.chaoxing.com/front/apps/seat/index",
                "https://office.chaoxing.com/front/third/apps/seat/index"
            ]

            for url in urls_to_try:
                try:
                    response = self.requests.get(url, verify=False)
                    if response.status_code == 200:
                        # 检查是否有重定向到带参数的URL
                        if 'fidEnc=' in response.url:
                            fidEnc_match = re.search(r'fidEnc=([^&]+)', response.url)
                            if fidEnc_match:
                                self.fidEnc = fidEnc_match.group(1)
                                if self.debug:
                                    logging.info(f"从重定向URL提取到fidEnc: {self.fidEnc}")
                        break
                except:
                    continue

        except Exception as e:
            if self.debug:
                logging.error(f"替代方法提取参数失败: {e}")

    def use_fallback_params(self):
        """使用后备参数"""
        if not self.fidEnc:
            self.fidEnc = "a76ca29f42cc102d"  # 使用你提供的值作为后备
        if not self.deptId:
            self.deptId = "47242"  # 从你的API响应中看到的deptId

        # 假设deptIdEnc就是fidEnc（这需要验证）
        if not self.deptIdEnc:
            self.deptIdEnc = self.fidEnc

        if self.debug:
            logging.info(f"使用后备参数 - fidEnc: {self.fidEnc}, deptId: {self.deptId}, deptIdEnc: {self.deptIdEnc}")

    def perform_verification_steps(self):
        """执行新增的验证步骤"""
        if not self.fidEnc:
            if self.debug:
                logging.warning("fidEnc参数未获取到，跳过验证步骤")
            return

        try:
            # 1. 获取入口配置
            self.get_entrance_config()

            # 2. 获取座位系统首页数据
            seat_data = self.get_seat_index()

            # 从座位数据中尝试提取deptId
            if seat_data and 'data' in seat_data:
                seat_config = seat_data['data'].get('seatConfig', {})
                if 'deptId' in seat_config:
                    self.deptId = str(seat_config['deptId'])
                    # 假设deptIdEnc就是fidEnc，这需要进一步验证
                    if not self.deptIdEnc:
                        self.deptIdEnc = self.fidEnc
                    if self.debug:
                        logging.info(f"从座位数据中提取到deptId: {self.deptId}")

            # 3. 身份验证
            self.verify_identity()

            if self.debug:
                logging.info("所有验证步骤完成")

        except Exception as e:
            if self.debug:
                logging.error(f"验证步骤执行失败: {e}")
            # 不抛出异常，继续执行后续流程

    def get_entrance_config(self):
        """获取入口配置"""
        params = {
            "appType": "0",
            "fidEnc": self.fidEnc
        }

        response = self.requests.get(
            url=self.entrance_config_url,
            params=params,
            headers=self.get_api_headers(),
            verify=False
        )

        if self.debug:
            logging.info(f"入口配置响应: {response.text}")

        return response.json()

    def get_seat_index(self):
        """获取座位系统首页数据"""
        params = {
            "fidEnc": self.fidEnc
        }

        response = self.requests.get(
            url=self.seat_index_url,
            params=params,
            headers=self.get_api_headers(),
            verify=False
        )

        if self.debug:
            logging.info(f"座位首页数据响应: {response.text}")

        return response.json()

    def verify_identity(self):
        """身份验证"""
        params = {
            "mappId": "0"
        }

        response = self.requests.get(
            url=self.identity_verify_url,
            params=params,
            headers=self.get_api_headers(),
            verify=False
        )

        if self.debug:
            logging.info(f"身份验证响应: {response.text}")

        return response.json()

    # extra: get roomid
    def roomid(self, encode):
        url = f"https://office.chaoxing.com/data/apps/seat/room/list?cpage=1&pageSize=100&firstLevelName=&secondLevelName=&thirdLevelName=&deptIdEnc={encode}"
        json_data = self.requests.get(url=url).content.decode('utf-8')
        ori_data = json.loads(json_data)
        for i in ori_data["data"]["seatRoomList"]:
            info = f'{i["firstLevelName"]}-{i["secondLevelName"]}-{i["thirdLevelName"]} id为：{i["id"]}'
            print(info)

    # solve captcha 

    def resolve_captcha(self):
        logging.info(f"Start to resolve captcha token")
        captcha_token, bg, tp = self.get_slide_captcha_data()
        logging.info(f"Successfully get prepared captcha_token {captcha_token}")
        logging.info(f"Captcha Image URL-small {tp}, URL-big {bg}")
        x = self.x_distance(bg, tp)
        logging.info(f"Successfully calculate the captcha distance {x}")

        params = {
            "callback": "jQuery33109180509737430778_1716381333117",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "token": captcha_token,
            "textClickArr": json.dumps([{"x": x}]),
            "coordinate": json.dumps([]),
            "runEnv": "10",
            "version": "1.1.18",
            "_": int(time.time() * 1000)
        }
        response = self.requests.get(
            f'https://captcha.chaoxing.com/captcha/check/verification/result', params=params, headers=self.headers)
        text = response.text.replace('jQuery33109180509737430778_1716381333117(', "").replace(')', "")
        data = json.loads(text)
        logging.info(f"Successfully resolve the captcha token {data}")
        try: 
           validate_val = json.loads(data["extraData"])['validate']
           return validate_val
        except KeyError as e:
            logging.info("Can't load validate value. Maybe server return mistake.")
            return ""

    def get_slide_captcha_data(self):
        url = "https://captcha.chaoxing.com/captcha/get/verification/image"
        timestamp = int(time.time() * 1000)
        capture_key, token = generate_captcha_key(timestamp)
        referer = f"https://office.chaoxing.com/front/third/apps/seat/code?id=3993&seatNum=0199"
        params = {
            "callback": f"jQuery33107685004390294206_1716461324846",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "version": "1.1.18",
            "captchaKey": capture_key,
            "token": token,
            "referer": referer,
            "_": timestamp,
            "d": "a",
            "b": "a"
        }
        response = self.requests.get(url=url, params=params, headers=self.headers)
        content = response.text
        
        data = content.replace("jQuery33107685004390294206_1716461324846(",
                            ")").replace(")", "")
        data = json.loads(data)
        captcha_token = data["token"]
        bg = data["imageVerificationVo"]["shadeImage"]
        tp = data["imageVerificationVo"]["cutoutImage"]
        return captcha_token, bg, tp
    
    def x_distance(self, bg, tp):
        import numpy as np
        import cv2
        def cut_slide(slide):
            slider_array = np.frombuffer(slide, np.uint8)
            slider_image = cv2.imdecode(slider_array, cv2.IMREAD_UNCHANGED)
            slider_part = slider_image[:, :, :3]
            mask = slider_image[:, :, 3]
            mask[mask != 0] = 255
            x, y, w, h = cv2.boundingRect(mask)
            cropped_image = slider_part[y:y + h, x:x + w]
            return cropped_image
        c_captcha_headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha-b.chaoxing.com",
            "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        bgc, tpc = self.requests.get(bg, headers=c_captcha_headers), self.requests.get(tp, headers=c_captcha_headers)
        bg, tp = bgc.content, tpc.content 
        bg_img = cv2.imdecode(np.frombuffer(bg, np.uint8), cv2.IMREAD_COLOR)  
        tp_img = cut_slide(tp)
        bg_edge = cv2.Canny(bg_img, 100, 200)
        tp_edge = cv2.Canny(tp_img, 100, 200)
        bg_pic = cv2.cvtColor(bg_edge, cv2.COLOR_GRAY2RGB)
        tp_pic = cv2.cvtColor(tp_edge, cv2.COLOR_GRAY2RGB)
        res = cv2.matchTemplate(bg_pic, tp_pic, cv2.TM_CCOEFF_NORMED)
        _, _, _, max_loc = cv2.minMaxLoc(res)  
        tl = max_loc
        return tl[0]

    def submit(self, times, roomid, seatid, action, return_detail=False):
        for seat in seatid:
            suc = False
            for attempt in range(self.max_attempt):
                # 获取预约日期
                delta_day = 1 if self.reserve_next_day else 0
                day = datetime.date.today() + datetime.timedelta(days=0+delta_day)
                if action:
                    day = datetime.date.today() + datetime.timedelta(days=1+delta_day)

                # 在获取token前添加延迟，模拟用户浏览行为
                if attempt > 0:  # 第一次尝试不延迟，后续尝试添加延迟
                    self.human_like_delay(1.0, 3.0)

                # 使用新的URL格式获取token
                select_page_url = self.select_url.format(self.deptIdEnc, roomid, str(day), self.fidEnc)
                token = self._get_page_token(select_page_url)
                if self.debug:
                    logging.info(f"Get token from {select_page_url}: {token}")

                # 在获取验证码前添加短暂延迟
                if self.enable_slider:
                    self.human_like_delay(0.5, 1.5)

                captcha = self.resolve_captcha() if self.enable_slider else ""
                if self.debug:
                    logging.info(f"Captcha token {captcha}")

                result = self.get_submit(self.submit_url, times=times,token=token, roomid=roomid, seatid=seat, captcha=captcha, action=action)

                # 检查是否触发了风控
                if isinstance(result, dict):
                    if '当前使用人数较多，请5分钟后再次尝试提交' in result.get('msg', ''):
                        if self.debug:
                            logging.info(f"触发风控，等待30-60秒后重试...")
                        # 等待30-60秒，模拟用户等待行为
                        wait_time = random.uniform(30, 60)
                        time.sleep(wait_time)
                        continue  # 继续下一次尝试

                    # 检查是否已预约或已过期
                    if result.get('msg') == '该时间段您已有预约！':
                        # 打印已预约的座位信息
                        seat_info = result.get('data', {}).get('seatReserve', {})
                        seat_num = seat_info.get('seatNum', seat)
                        room_id = seat_info.get('roomId', roomid)
                        room_name = seat_info.get('firstLevelName', '')
                        print(f"用户已在该时间段预约了座位: 房间ID:{room_id} 房间名:{room_name} 座位号:{seat_num}")
                        # 已有预约表示成功，但继续尝试其他座位
                        suc = True
                        # 不break，继续尝试其他座位
                    elif result.get('msg') == '该时间段已过，不可预约！':
                        if self.debug:
                            logging.info('检测到该时间段已过，不可预约，立即终止脚本。')
                        raise Exception('该时间段已过，不可预约，终止脚本')
                suc = result if isinstance(result, bool) else result.get('success', False)
                if return_detail:
                    params = {
                        'roomId': roomid,
                        'startTime': times[0],
                        'endTime': times[1],
                        'day': str(self._get_reserve_day(action)),
                        'seatNum': seat
                    }
                    if isinstance(result, dict):
                        result = dict(result)
                        result['params'] = params
                        return result
                    else:
                        return {'success': suc, 'params': params}
                if suc:
                    return suc
                time.sleep(self.sleep_time)
        return suc

    def _get_reserve_day(self, action):
        delta_day = 1 if self.reserve_next_day else 0
        day = datetime.date.today() + datetime.timedelta(days=0+delta_day)
        if action:
            day = datetime.date.today() + datetime.timedelta(days=1+delta_day)
        return day

    def get_submit(self, url, times, token, roomid, seatid, captcha="", action=False):
        delta_day = 1 if self.reserve_next_day else 0
        day = datetime.date.today() + datetime.timedelta(days=0+delta_day)  # 预约今天，修改days=1表示预约明天
        if action:
            day = datetime.date.today() + datetime.timedelta(days=1+delta_day)  # 由于action时区问题导致其早+8区一天
        parm = {
            "deptIdEnc": self.deptIdEnc,  # 新增的部门ID加密参数
            "roomId": roomid,
            "startTime": times[0],
            "endTime": times[1],
            "day": str(day),
            "seatNum": seatid,
            "captcha": captcha,
            "token": token
        }
        if self.debug:
            logging.info(f"submit parameter {parm} ")
        parm["enc"] = enc(parm)

        # 添加人类行为延迟
        self.human_like_delay(0.3, 1.2)

        # 设置正确的referer
        referer = f"https://office.chaoxing.com/front/third/apps/seat/select?deptIdEnc={self.deptIdEnc}&id={roomid}&day={str(day)}&backLevel=2&fidEnc={self.fidEnc}"
        submit_headers = self.submit_headers.copy()
        submit_headers["Referer"] = referer

        if self.debug:
            logging.info(f"提交请求头: {submit_headers}")

        # 使用GET请求而不是POST，因为从你的请求看是GET方式
        html = self.requests.get(
            url=url, params=parm, headers=submit_headers, verify=True).content.decode('utf-8')
        self.submit_msg.append(
            times[0] + "~" + times[1] + ':  ' + str(json.loads(html)))
        if self.debug:
            logging.info(json.loads(html))
        # 始终返回完整响应字典
        return json.loads(html)
