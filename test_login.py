#!/usr/bin/env python3
"""
测试脚本：验证登录和参数提取
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.reserve import reserve
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_login_and_params():
    """测试登录和参数提取功能"""
    print("开始测试登录和参数提取...")
    
    # 创建reserve实例
    s = reserve(debug=True)
    
    # 获取登录状态
    s.get_login_status()
    
    # 测试登录（使用配置文件中的用户名和密码）
    username = "17607062006"
    password = "ab.135246"
    
    print(f"\n=== 测试登录 ===")
    print(f"用户名: {username}")
    
    result = s.login(username, password)
    print(f"登录结果: {result}")
    
    print(f"\n=== 登录后的参数 ===")
    print(f"fidEnc: {s.fidEnc}")
    print(f"deptId: {s.deptId}")
    print(f"deptIdEnc: {s.deptIdEnc}")
    
    # 测试获取token
    if s.deptIdEnc and s.fidEnc:
        print(f"\n=== 测试获取token ===")
        roomid = "8886"
        day = "2025-07-31"
        select_page_url = s.select_url.format(s.deptIdEnc, roomid, day, s.fidEnc)
        print(f"Token获取URL: {select_page_url}")
        
        try:
            token = s._get_page_token(select_page_url)
            print(f"获取到的token: {token}")
        except Exception as e:
            print(f"获取token失败: {e}")

if __name__ == "__main__":
    test_login_and_params()
