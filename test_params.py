#!/usr/bin/env python3
"""
测试脚本：验证deptIdEnc参数的提取
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.reserve import reserve
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_param_extraction():
    """测试参数提取功能"""
    print("开始测试参数提取...")
    
    # 创建reserve实例
    s = reserve(debug=True)
    
    # 模拟登录状态
    s.get_login_status()
    
    # 测试参数提取
    print("\n=== 测试参数提取 ===")
    s.extract_required_params()
    
    print(f"fidEnc: {s.fidEnc}")
    print(f"deptId: {s.deptId}")
    print(f"deptIdEnc: {s.deptIdEnc}")
    
    # 测试验证步骤
    if s.fidEnc:
        print("\n=== 测试验证步骤 ===")
        s.perform_verification_steps()
    else:
        print("fidEnc未获取到，跳过验证步骤测试")

if __name__ == "__main__":
    test_param_extraction()
