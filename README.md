# 智能座位预约系统（含备选房间功能）

本项目支持自动化预约座位，支持多房间、备选座位、调试模式等功能，适用于超星等自习室预约场景。下文为详细功能与配置说明：

## 全局参数说明

| 参数名            | 类型    | 默认值      | 说明                                             |
|-------------------|---------|-------------|--------------------------------------------------|
| SLEEPTIME         | float   | 0.2         | 每次抢座请求的间隔秒数，防止请求过快被限流        |
| ENABLE_SLIDER     | bool    | False       | 是否启用滑块验证码处理（如遇到滑块验证可开启）    |
| MAX_ATTEMPT       | int     | 4           | 每个用户最大尝试次数                             |
| RESERVE_NEXT_DAY  | bool    | False       | 是否预约明天（True为预约明天，False为今天）      |
| IMMEDIATE_RUN     | bool    | False       | 立即运行，无需等待目标时间，便于调试              |
| DEBUG             | bool    | False       | 是否输出详细调试信息，便于排查问题                |
| TARGET_TIME       | string  | 22:57:00    | 抢座目标时间（格式：HH:MM:SS）                   |
| PREPARE_SECONDS   | int     | 10          | 提前多少秒准备（登录），防止错过目标时间          |

> 你可以在 main.py 顶部直接修改这些参数以适配你的需求。

---

## 功能概述

支持为每个用户配置多个房间及各自的座位列表。系统会按顺序依次尝试预约房间和座位，提高预约成功率。支持新格式配置，结构清晰灵活。

---

## 配置字段说明

每个用户配置支持以下字段：

| 字段         | 类型         | 必须 | 说明                                                                 |
| ------------ | ------------ | ---- | -------------------------------------------------------------------- |
| username     | string       | 是   | 用户名/手机号                                                        |
| password     | string       | 是   | 密码                                                                 |
| alias        | string       | 否   | 昵称，仅用于日志显示                                                 |
| times/time/时间段 | array/array/array | 是   | 预约时间段，格式如 ["20:30", "22:00"]，支持 times、time、时间段 三种写法 |
| rooms        | array        | 是   | 新格式，房间与座位独立配置，见下方示例                              |
| daysofweek   | array        | 是   | 预约星期，1-7分别代表周一到周日                                      |

---

## 推荐配置格式（新格式）

每个房间独立配置座位，推荐使用：

```json
{
  "reserve": [
    {
      "username": "176xxxxxxxx",
      "alias": "小明",
      "password": "password",
      "times": ["20:30", "22:00"],
      "rooms": [
        {
          "roomid": "8886",
          "seatid": ["256", "258"]
        },
        {
          "roomid": "8885",
          "seatid": ["100", "101"]
        }
      ],
      "daysofweek": [1,2,3,4,5]
    }
  ]
}
```

---

## 字段详细说明

- `rooms`：数组，每个元素为一个房间配置，包含 `roomid` 和 `seatid`（可为字符串或数组）。
- `times`/`time`/`时间段`：任选其一，均为长度为2的数组，表示预约起止时间。
- `daysofweek`：数组，1-7分别代表周一到周日，决定哪些天会尝试预约。

---

## 执行流程

1. **优先级顺序**：系统会按照配置中的房间顺序依次尝试。
2. **独立座位配置**：每个房间可配置不同的座位列表。
3. **成功即停**：一旦预约成功即停止后续尝试。
4. **失败继续**：若房间所有座位均失败，则继续下一个房间。
5. **日期判断**：仅在 `daysofweek` 包含当天时才会尝试预约。

---

## 调试与日志

- 设置 `DEBUG = True` 可输出详细日志，显示每次尝试的房间、座位、结果等。
- 日志示例：
  ```
  ----------- 用户: 17607062006 -- 时间段: ['20:30', '22:00'] -- 房间配置: [{'roomid': '8886', 'seatid': ['256', '258']}, {'roomid': '8885', 'seatid': ['100', '101']}] 第1/4次尝试 -----------
  尝试房间: 8886，座位: ['256', '258']
  尝试房间 8886 座位: 256
  尝试房间 8886 座位: 258
  尝试房间: 8885，座位: ['100', '101']
  尝试房间 8885 座位: 100
  ```

---

## 其他说明

- **建议**：优先将最想要的房间和座位放在前面。
- **房间/座位有效性**：请确保配置的房间ID和座位号真实有效，否则会导致预约失败。
- **IMMEDIATE_RUN**：如需调试可将 `IMMEDIATE_RUN = True`，跳过等待直接运行。

---

如需进一步扩展或有特殊需求，可参考 `main.py` 及 `utils/reserve.py` 相关实现。 