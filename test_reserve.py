#!/usr/bin/env python3
"""
测试脚本：验证预约功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.reserve import reserve
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_reservation():
    """测试预约功能"""
    print("开始测试预约功能...")
    
    # 创建reserve实例
    s = reserve(debug=True)
    
    # 获取登录状态
    s.get_login_status()
    
    # 登录
    username = "17607062006"
    password = "ab.135246"
    
    print(f"\n=== 登录 ===")
    result = s.login(username, password)
    print(f"登录结果: {result}")
    
    if result[0]:  # 登录成功
        print(f"\n=== 参数信息 ===")
        print(f"fidEnc: {s.fidEnc}")
        print(f"deptId: {s.deptId}")
        print(f"deptIdEnc: {s.deptIdEnc}")
        
        # 测试预约
        print(f"\n=== 测试预约 ===")
        times = ["21:30", "22:00"]
        roomid = "8886"
        seatid = ["223"]
        
        try:
            result = s.submit(times=times, roomid=roomid, seatid=seatid, action=False, return_detail=True)
            print(f"预约结果: {result}")
        except Exception as e:
            print(f"预约失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("登录失败，无法进行预约测试")

if __name__ == "__main__":
    test_reservation()
