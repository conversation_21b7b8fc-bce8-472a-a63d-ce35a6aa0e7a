import json
import time
import argparse
import os
import logging
import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

from utils import reserve, get_user_credentials
get_current_time = lambda action: time.strftime("%H:%M:%S", time.localtime(time.time() + 8*3600)) if action else time.strftime("%H:%M:%S", time.localtime(time.time()))
get_current_dayofweek = lambda action: time.strftime("%A", time.localtime(time.time() + 8*3600)) if action else time.strftime("%A", time.localtime(time.time()))

SLEEPTIME = 0.2 # 每次抢座的间隔
ENABLE_SLIDER = False # 是否有滑块验证
MAX_ATTEMPT = 4 # 最大尝试次数，每个用户最多尝试5次
RESERVE_NEXT_DAY = True # 预约明天而不是今天的
IMMEDIATE_RUN = True  # 立即运行，无需等待目标时间，便于调试
DEBUG = False  # 是否输出详细调试信息，默认只输出简洁信息
TARGET_TIME = "23:30:00"  # 抢座目标时间
PREPARE_SECONDS = 10      # 提前多少秒准备（登录）

# 全局变量用于跟踪系统错误显示
SYSTEM_ERROR_SHOWN = False

# 仅支持 daysofweek 为数字1-7（1=周一，7=周日）
WEEKDAY_MAP = {
    1: 'Monday', 2: 'Tuesday', 3: 'Wednesday', 4: 'Thursday', 5: 'Friday', 6: 'Saturday', 7: 'Sunday',
    '1': 'Monday', '2': 'Tuesday', '3': 'Wednesday', '4': 'Thursday', '5': 'Friday', '6': 'Saturday', '7': 'Sunday'
}

def is_today_in_user_days(user, current_dayofweek):
    days = user.get('daysofweek', [])
    # 只允许数字
    mapped_days = [WEEKDAY_MAP[str(d)] for d in days]
    return current_dayofweek in mapped_days

def login_and_reserve(users, usernames, passwords, action, success_list=None):
    logging.info(f"全局设置: \n抢座间隔: {SLEEPTIME}\n滑块验证: {ENABLE_SLIDER}\n预约明天: {RESERVE_NEXT_DAY}")
    if action and len(usernames.split(",")) != len(users):
        raise Exception("用户数量应与配置数量一致")
    if success_list is None:
        success_list = [False] * len(users)
    current_dayofweek = get_current_dayofweek(action)
    for index, user in enumerate(users):
        username, password, times, roomid, seatid, daysofweek = user.values()
        if action:
            username, password = usernames.split(',')[index], passwords.split(',')[index]
        if(current_dayofweek not in daysofweek):
            logging.info("今天不在预约设置的日期内，跳过该用户")
            continue
        if not success_list[index]: 
            rooms_config = get_rooms_config(user)  # 获取房间配置
            logging.info(f"----------- 用户: {username} -- 时间段: {times} -- 房间配置: {rooms_config} 开始尝试 -----------")
            s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
            s.get_login_status()
            s.login(username, password)
            s.requests.headers.update({'Host': 'office.chaoxing.com'})
            for room_config in rooms_config:
                roomid = room_config['roomid']
                seatids = room_config['seatid'] if isinstance(room_config['seatid'], list) else [room_config['seatid']]
                for seat in seatids:
                    suc = s.submit(times, roomid, [seat], action)
                    if suc:
                        break
                if suc:
                    break
            success_list[index] = suc
    return success_list

def wait_until_prepare(target_time_str, prepare_seconds=10):
    """倒计时到准备时间点（目标时间-prepare_seconds）"""
    now = datetime.datetime.now()
    today_str = now.strftime("%Y-%m-%d")
    target_dt = datetime.datetime.strptime(f"{today_str} {target_time_str}", "%Y-%m-%d %H:%M:%S")
    prepare_dt = target_dt - datetime.timedelta(seconds=prepare_seconds)
    now = datetime.datetime.now()
    if now > target_dt:
        print(f"{now.strftime('%m-%d %H:%M:%S')} 已超过脚本设置的启动时间({target_time_str})，脚本自动退出。")
        exit(0)
    while True:
        now = datetime.datetime.now()
        left = (prepare_dt - now).total_seconds()
        now_str = now.strftime('%m-%d %H:%M:%S')
        if left > 0:
            print(f"{now_str} 距离进入登录准备状态还有 {int(left)} 秒...", end='\r', flush=True)
            time.sleep(1)
        else:
            break
    print(f"\n{datetime.datetime.now().strftime('%m-%d %H:%M:%S')} 进入准备状态，开始登录...")
    return target_dt

def reserve_user_thread(user, user_session, action, user_idx, success_list, user_attempts, current_dayofweek):
    """单个用户的抢座线程函数"""
    if success_list[user_idx]:
        return user_idx, True, "已成功"
    
    if user_attempts[user_idx] >= MAX_ATTEMPT:
        return user_idx, False, "达到最大尝试次数"
    
    if not is_today_in_user_days(user, current_dayofweek):
        return user_idx, False, "今天不在预约设置的日期内"
    
    # 检查预约时间是否已过（仅预约今日时）
    if not RESERVE_NEXT_DAY:
        times = get_time_key(user)
        end_time_str = times[1]
        now_dt = datetime.datetime.now()
        end_dt = now_dt.replace(hour=int(end_time_str.split(':')[0]), minute=int(end_time_str.split(':')[1]), second=0, microsecond=0)
        if now_dt > end_dt:
            alias = user.get('alias', user['username'])
            return user_idx, False, f"配置的预约时间段已过（{times[0]}~{times[1]}）"
    
    rooms_config = get_rooms_config(user)
    if DEBUG:
        logging.info(f"----------- 用户: {user['username']} -- 时间段: {get_time_key(user)} -- 房间配置: {rooms_config} 第{user_attempts[user_idx]+1}/{MAX_ATTEMPT}次尝试 -----------")
    
    s = user_session
    suc = False
    user_failed = False
    
    try:
        for room_config in rooms_config:
            roomid = room_config['roomid']
            seatids = room_config['seatid'] if isinstance(room_config['seatid'], list) else [room_config['seatid']]
            if DEBUG:
                logging.info(f"尝试房间: {roomid}，座位: {seatids}")
            for seat in seatids:
                if DEBUG:
                    logging.info(f"尝试房间 {roomid} 座位: {seat}")
                times = get_time_key(user)
                result = s.submit(times, roomid, [seat], action, return_detail=True)
                
                if not DEBUG:
                    # 收集用户结果，稍后按用户分组显示
                    alias = user.get('alias', user['username'])
                    msg = result.get('msg') if isinstance(result, dict) else ''
                    success = result.get('success') if isinstance(result, dict) else result
                    params = result.get('params') if isinstance(result, dict) and 'params' in result else None
                    
                    # 将结果存储到全局变量中，稍后统一显示
                    if not hasattr(reserve_user_thread, 'user_outputs'):
                        reserve_user_thread.user_outputs = {}
                    
                    if alias not in reserve_user_thread.user_outputs:
                        reserve_user_thread.user_outputs[alias] = []
                    
                    try:
                        alias_str = str(alias)
                        alias_str = alias_str.encode('utf-8').decode('utf-8')
                    except:
                        alias_str = alias
                    
                    if params:
                        status_icon = "✅" if success else "❌"
                        output_line = f"{status_icon} 房间{params.get('roomId')}-座位{params.get('seatNum')} | {msg}"
                    else:
                        status_icon = "✅" if success else "❌"
                        output_line = f"{status_icon} {msg}"
                    
                    reserve_user_thread.user_outputs[alias].append(output_line)
                
                suc = result if isinstance(result, bool) else result.get('success', False)
                if isinstance(result, dict):
                    msg = result.get('msg', '')
                    if msg == '该时间段您已有预约！':
                        # 已有预约表示成功，但继续尝试其他座位
                        suc = True
                        # 不break，继续尝试其他座位
                    elif msg == '该时间段已过，不可预约！':
                        user_failed = True
                        break
                if suc:
                    break
            if user_failed:
                break
    except Exception as e:
        if '该时间段已有预约' in str(e) or '该时间段已过，不可预约' in str(e):
            user_failed = True
        else:
            raise
    
    return user_idx, suc, "完成"

def main(users, action=False):
    # 1. 倒计时到准备时间点
    if not IMMEDIATE_RUN:
        target_dt = wait_until_prepare(TARGET_TIME, PREPARE_SECONDS)
    else:
        target_dt = datetime.datetime.now()
        print(f"立即运行模式，跳过等待，直接进入抢座流程。当前时间: {target_dt.strftime('%m-%d %H:%M:%S')}")
    # 2. 登录所有用户
    usernames, passwords = None, None
    if action:
        usernames, passwords = get_user_credentials(action)
    user_sessions = []
    for idx, user in enumerate(users):
        s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY, debug=DEBUG)
        s.get_login_status()
        username, password = user['username'], user['password']
        if action:
            username, password = usernames.split(',')[idx], passwords.split(',')[idx]
        s.login(username, password)
        s.requests.headers.update({'Host': 'office.chaoxing.com'})
        user_sessions.append(s)
    print(f"{datetime.datetime.now().strftime('%m-%d %H:%M:%S')} 登录完成，等待抢座开始...")
    # 3. 等待到目标时间
    while True:
        now = datetime.datetime.now()
        if now >= target_dt:
            print(f"{now.strftime('%m-%d %H:%M:%S')} 到达目标时间 {TARGET_TIME}，开始抢座！")
            break
        left = (target_dt - now).total_seconds()
        print(f"{now.strftime('%m-%d %H:%M:%S')} 距离抢座开始还有 {int(left)} 秒...", end='\r', flush=True)
        time.sleep(1)
    # 4. 抢座主循环 - 多线程版本
    current_time = get_current_time(action)
    logging.info(f"脚本启动时间 {current_time}，action {'开启' if action else '关闭'}")
    logging.info(f"全局设置: \n抢座间隔: {SLEEPTIME}\n滑块验证: {ENABLE_SLIDER}\n预约明天: {RESERVE_NEXT_DAY}")
    attempt_times = 0
    success_list = None
    current_dayofweek = get_current_dayofweek(action)
    today_reservation_num = sum(1 for d in users if is_today_in_user_days(d, current_dayofweek))
    user_attempts = [0] * len(users)
    
    print(f"启用多线程抢座模式，所有用户将同时开始抢座！")
    
    while True:
        attempt_times += 1
        if success_list is None:
            success_list = [False] * len(users)
        
        # 创建线程池，同时处理所有用户
        with ThreadPoolExecutor(max_workers=len(users)) as executor:
            # 提交所有用户的抢座任务
            future_to_user = {}
            for idx, user in enumerate(users):
                if success_list[idx]:
                    continue
                if user_attempts[idx] >= MAX_ATTEMPT:
                    continue
                if not is_today_in_user_days(user, current_dayofweek):
                    if DEBUG:
                        logging.info("今天不在预约设置的日期内，跳过该用户")
                    continue
                
                # 检查预约时间是否已过（仅预约今日时）
                if not RESERVE_NEXT_DAY:
                    times = get_time_key(user)
                    end_time_str = times[1]
                    now_dt = datetime.datetime.now()
                    end_dt = now_dt.replace(hour=int(end_time_str.split(':')[0]), minute=int(end_time_str.split(':')[1]), second=0, microsecond=0)
                    if now_dt > end_dt:
                        alias = user.get('alias', user['username'])
                        print(f"{now_dt.strftime('%m-%d %H:%M:%S')} 用户: {alias} 配置的预约时间段已过（{times[0]}~{times[1]}），跳过该用户。")
                        continue
                
                # 提交任务到线程池
                future = executor.submit(
                    reserve_user_thread, 
                    user, 
                    user_sessions[idx], 
                    action, 
                    idx, 
                    success_list, 
                    user_attempts, 
                    current_dayofweek
                )
                future_to_user[future] = idx
            
            # 等待所有任务完成并收集结果
            user_results = {}  # 用于收集每个用户的结果
            for future in as_completed(future_to_user):
                user_idx, suc, status = future.result()
                success_list[user_idx] = suc
                user_attempts[user_idx] += 1
                
                # 收集用户结果
                if user_idx not in user_results:
                    user_results[user_idx] = []
                user_results[user_idx].append({
                    'success': suc,
                    'status': status,
                    'attempt': user_attempts[user_idx]
                })
                
                if DEBUG:
                    alias = users[user_idx].get('alias', users[user_idx]['username'])
                    print(f"用户 {alias} 第{user_attempts[user_idx]}/{MAX_ATTEMPT}次尝试完成，状态: {status}")
            
            # 按用户分组输出详细信息
            if not DEBUG:
                print(f"\n📊 第{attempt_times}轮尝试结果:")
                
                # 检查是否有系统错误
                global SYSTEM_ERROR_SHOWN
                if not SYSTEM_ERROR_SHOWN and hasattr(reserve_user_thread, 'user_outputs'):
                    for outputs in reserve_user_thread.user_outputs.values():
                        for output in outputs:
                            if '所选时段未在系统中开放' in output:
                                print(f"⚠️  系统提示: 所选时段未在系统中开放，请检查预约时间设置")
                                SYSTEM_ERROR_SHOWN = True
                                break
                        if SYSTEM_ERROR_SHOWN:
                            break
                
                # 按用户分组显示结果
                for user_idx, results in user_results.items():
                    alias = users[user_idx].get('alias', users[user_idx]['username'])
                    success_count = sum(1 for r in results if r['success'])
                    total_attempts = len(results)
                    status_icon = "✅" if success_list[user_idx] else "⏳"
                    
                    print(f"\n👤 {alias} (第{user_attempts[user_idx]}/{MAX_ATTEMPT}次尝试):")
                    
                    # 显示该用户的详细输出
                    if hasattr(reserve_user_thread, 'user_outputs') and alias in reserve_user_thread.user_outputs:
                        for output in reserve_user_thread.user_outputs[alias]:
                            print(f"  {output}")
                    
                    # 显示汇总状态
                    print(f"  📈 状态: {success_count}/{total_attempts} 成功")
                
                # 清空用户输出缓存，准备下一轮
                if hasattr(reserve_user_thread, 'user_outputs'):
                    reserve_user_thread.user_outputs.clear()
                
                print("-" * 60)
        
        if DEBUG:
            print(f"第{attempt_times}轮尝试，当前时间 {get_current_time(action)}，成功列表: {success_list}")
        
        if sum(success_list) == today_reservation_num:
            print(f"全部预约成功！")
            return
        
        # 检查是否所有用户都被跳过或达到最大尝试次数
        all_skipped = True
        for idx, user in enumerate(users):
            if not success_list[idx] and user_attempts[idx] < MAX_ATTEMPT and is_today_in_user_days(user, current_dayofweek):
                all_skipped = False
                break
        
        if all_skipped:
            print("所有用户均被跳过（如预约时间已过或达到最大尝试次数），脚本结束。")
            break

def debug(users, action=False):
    logging.info(f"全局设置: \n抢座间隔: {SLEEPTIME}\n滑块验证: {ENABLE_SLIDER}\n预约明天: {RESERVE_NEXT_DAY}")
    suc = False
    logging.info(f" Debug Mode start! , action {'开启' if action else '关闭'}")
    if action:
        usernames, passwords = get_user_credentials(action)
    current_dayofweek = get_current_dayofweek(action)
    for index, user in enumerate(users):
        username, password, times, roomid, seatid, daysofweek = user.values()
        if type(seatid) == str:
            seatid = [seatid]
        if action:
            username ,password = usernames.split(',')[index], passwords.split(',')[index]
        if(current_dayofweek not in daysofweek):
            logging.info("今天不在预约设置的日期内，跳过该用户")
            continue
        rooms_config = get_rooms_config(user)  # 获取房间配置
        logging.info(f"----------- 用户: {username} -- 时间段: {times} -- 房间配置: {rooms_config} 开始尝试 -----------")
        s = reserve(sleep_time=SLEEPTIME,  max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
        s.get_login_status()
        s.login(username, password)
        s.requests.headers.update({'Host': 'office.chaoxing.com'})
        for room_config in rooms_config:
            roomid = room_config['roomid']
            seatids = room_config['seatid'] if isinstance(room_config['seatid'], list) else [room_config['seatid']]
            if DEBUG:
                logging.info(f"尝试房间: {roomid}，座位: {seatids}")
            for seat in seatids:
                if DEBUG:
                    logging.info(f"尝试房间 {roomid} 座位: {seat}")
                suc = s.submit(times, roomid, [seat], action)
                if suc:
                    return

def get_roomid(args1, args2):
    username = input("请输入用户名：")
    password = input("请输入密码：")
    s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
    s.get_login_status()
    s.login(username=username, password=password)
    s.requests.headers.update({'Host': 'office.chaoxing.com'})
    encode = input("请输入deptldEnc：")
    s.roomid(encode)

def get_time_key(user):
    if 'times' in user:
        return user['times']
    elif 'time' in user:
        return user['time']
    elif '时间段' in user:
        return user['时间段']
    else:
        raise KeyError('用户配置缺少时间段字段（times/time/时间段）')

def get_room_key(user):
    """获取房间ID列表，支持单个房间或房间列表（向后兼容）"""
    if 'roomid' in user:
        roomid = user['roomid']
        if isinstance(roomid, list):
            return roomid
        elif isinstance(roomid, str):
            # 支持 "8886" 或 "8886,8885" 格式
            if ',' in roomid:
                return [r.strip() for r in roomid.split(',')]
            else:
                return [roomid]
        else:
            return [str(roomid)]
    elif 'room' in user:
        room = user['room']
        if isinstance(room, list):
            return room
        elif isinstance(room, str):
            if ',' in room:
                return [r.strip() for r in room.split(',')]
            else:
                return [room]
        else:
            return [str(room)]
    else:
        raise KeyError('用户配置缺少房间字段（roomid/room）')

def get_rooms_config(user):
    """获取房间配置，支持新的rooms格式和旧的roomid格式（向后兼容）"""
    if 'rooms' in user:
        # 新格式：每个房间有独立的座位配置
        return user['rooms']
    else:
        # 旧格式：向后兼容
        roomids = get_room_key(user)
        seatids = user.get('seatid', [])
        if isinstance(seatids, str):
            seatids = [seatids]
        
        # 将旧格式转换为新格式
        rooms = []
        for roomid in roomids:
            rooms.append({
                'roomid': roomid,
                'seatid': seatids
            })
        return rooms

if __name__ == "__main__":
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    parser = argparse.ArgumentParser(prog='Chao Xing seat auto reserve')
    parser.add_argument('-u','--user', default=config_path, help='user config file')
    parser.add_argument('-m','--method', default="reserve" ,choices=["reserve", "debug", "room"], help='for debug')
    parser.add_argument('-a','--action', action="store_true",help='use --action to enable in github action')
    args = parser.parse_args()
    func_dict = {"reserve": main, "debug":debug, "room": get_roomid}
    with open(args.user, "r+") as data:
        usersdata = json.load(data)["reserve"]
    func_dict[args.method](usersdata, args.action)
